{"name": "wheres-my-order", "version": "2.0.1", "license": "TODO: CHANGE ME ", "scripts": {"ng": "ng", "start": "node --max_old_space_size=8192 node_modules/@angular/cli/bin/ng serve", "build": "node --max_old_space_size=8192 node_modules/@angular/cli/bin/ng build", "build-prod": "node --max_old_space_size=8192 node_modules/@angular/cli/bin/ng build --configuration production", "analyze": "webpack-bundle-analyzer dist/wheres-my-order/stats.json", "test": "ng test", "ci-test": "ng test --karma-config ci.karma.conf.js --code-coverage --progress false --watch false --source-map false", "lint": "ng lint", "e2e": "ng e2e", "ci-e2e": "ng e2e --protractor-config e2e/ci.protractor.conf.js --webdriver-update=false", "compodoc": "npx compodoc -p tsconfig.json", "webdriver-update": "./node_modules/.bin/webdriver-manager update", "build-themes": "devextreme build", "postinstall": "npm run build-themes"}, "private": true, "dependencies": {"@angular/animations": "^14.2.4", "@angular/cdk": "^14.2.3", "@angular/common": "^14.2.4", "@angular/compiler": "^14.2.4", "@angular/core": "^14.2.4", "@angular/forms": "^14.2.4", "@angular/platform-browser": "^14.2.4", "@angular/platform-browser-dynamic": "^14.2.4", "@angular/router": "^14.2.4", "@azure/msal-angular": "^2.5.13", "@azure/msal-browser": "^2.38.4", "@azure/storage-blob": "^12.4.0", "@azure/storage-file-share": "^12.4.0", "@microsoft/signalr": "^6.0.9", "binary-loader": "^0.0.1", "clone-deep": "^4.0.1", "deep-object-diff": "^1.1.0", "devextreme": "^22.1.5", "devextreme-angular": "^22.1.5", "devextreme-schematics": "^1.3.3", "dompurify": "^2.4.0", "exceljs": "^4.2.0", "exifreader": "^4.2.0", "fabric": "^5.2.4", "file-saver": "^2.0.5", "font-awesome": "^4.7.0", "handlebars": "^4.7.7", "html2canvas": "^1.3.2", "intl": "1.2.5", "jspdf": "^2.5.0", "moment": "^2.29.1", "moment-timezone": "^0.5.45", "ngx-moment": "^6.0.2", "ngx-toastr": "^15.1.0", "ngx-webcam": "^0.4.1", "pluralize": "^8.0.0", "powerbi-client-angular": "^2.0.0", "rxjs": "^7.5.7", "tslib": "^2.3.0", "ua-parser-js": "^1.0.2", "uuid": "^9.0.0", "web-animations-js": "2.3.2", "zone.js": "^0.11.8"}, "devDependencies": {"@angular-devkit/build-angular": "^14.1.3", "@angular-eslint/builder": "~14.1.2", "@angular-eslint/eslint-plugin": "~14.1.2", "@angular-eslint/eslint-plugin-template": "~14.1.2", "@angular-eslint/schematics": "~14.1.2", "@angular-eslint/template-parser": "~14.1.2", "@angular/cli": "^14.2.4", "@angular/compiler-cli": "^14.2.4", "@angular/language-service": "^14.2.4", "@compodoc/compodoc": "~1.1.11", "@dhigroup/karma-vsts-reporter": "~1.3.0", "@types/clone-deep": "^4.0.1", "@types/dompurify": "^2.3.4", "@types/fabric": "^4.5.12", "@types/file-saver": "^2.0.1", "@types/jasmine": "^4.3.0", "@types/node": "^18.7.23", "@types/pluralize": "0.0.29", "@types/ua-parser-js": "^0.7.36", "@typescript-eslint/eslint-plugin": "^5.38.1", "@typescript-eslint/parser": "^5.38.1", "codelyzer": "^6.0.1", "devextreme-cli": "^1.2.24", "devextreme-themebuilder": "^22.1.5", "eslint": "^8.24.0", "eslint-plugin-jasmine": "^4.1.2", "jasmine-core": "^4.4.0", "jasmine-reporters": "^2.3.2", "jasmine-spec-reporter": "^7.0.0", "jasmine-tfs-reporter": "^1.0.2", "karma": "^6.4.1", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "^5.1.0", "karma-jasmine-html-reporter": "^2.0.0", "karma-junit-reporter": "^2.0.1", "karma-spec-reporter": "~0.0.32", "protractor": "~7.0.0", "typescript": "^4.8.4", "webpack-bundle-analyzer": "^4.6.1"}}