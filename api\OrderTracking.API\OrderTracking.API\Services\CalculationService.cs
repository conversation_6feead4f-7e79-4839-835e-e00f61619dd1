﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
// Migrated from Google Cloud Firestore to Azure Cosmos DB
// using Google.Cloud.Firestore;
using Microsoft.Azure.Cosmos;
using Internal;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json.Linq;
using OrderTracking.API.Extensions;
using OrderTracking.API.Interfaces;
using OrderTracking.API.Models;

namespace OrderTracking.API.Services
{
    /// <summary>
    ///     Service class for Calculations
    /// </summary>
    public class CalculationService : ICalculationService
    {
        // Migrated from Firestore CollectionReference to Azure Cosmos DB Container
        private readonly Container _container;
        private readonly ILogger<CalculationService> _logger;

        /// <summary>
        ///     Constructs the CalculationService class
        ///     instance
        /// </summary>
        /// <param name="cosmosClient">Azure Cosmos DB client</param>
        /// <param name="options">Connection options</param>
        /// <param name="logger">Logger instance</param>
        public CalculationService(CosmosClient cosmosClient, IOptions<Connections> options, ILogger<CalculationService> logger)
        {
            if (cosmosClient == null) throw new ArgumentNullException(nameof(cosmosClient));
            if (options == null) throw new ArgumentNullException(nameof(options));

            // Get the container for flange calculations
            var database = cosmosClient.GetDatabase(options.Value.CosmosDatabase ?? "OrderTrackingDB");
            _container = database.GetContainer(options.Value.FlangeCalculations ?? "FlangeCalculations");
            _logger = logger;
        }

        /// <summary>
        ///     Add an calculation
        /// </summary>
        /// <param name="calculation"></param>
        /// <returns></returns>
        public async Task<string> AddItemAsync(JObject calculation)
        {
            if (calculation == null) throw new ArgumentNullException(nameof(calculation));

            var calculationObject = calculation.ToObject<Dictionary<string, object>>();


            try
            {
                if (calculationObject["tighteningMethods"] != null)
                {
                    JArray jArray = (JArray)calculationObject["tighteningMethods"];
                    calculationObject["tighteningMethods"] = jArray.Select(x => x.ToString());
                }
                if (calculationObject["toolList"] != null)
                {
                    JArray jArray = (JArray)calculationObject["toolList"];
                    calculationObject["toolList"] = jArray.Select(x => x.ToString());
                }
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error occured while trying to cast Enumerable from JArray");
                throw;
            }


            // Migrated from Firestore to Azure Cosmos DB
            // Generate a unique ID for the document
            var id = Guid.NewGuid().ToString();
            calculationObject["id"] = id;

            // Create the item in Cosmos DB
            var response = await _container.CreateItemAsync(calculationObject, new PartitionKey(id));
            return response.Resource["id"].ToString();
        }

        /// <summary>
        ///     Delete an calculation
        /// </summary>
        /// <param name="id"></param>
        /// <param name="pk"></param>
        /// <returns></returns>
        public async Task DeleteItemAsync(string id, string pk)
        {
            // Migrated from Firestore to Azure Cosmos DB
            await _container.DeleteItemAsync<dynamic>(id, new PartitionKey(pk ?? id));
        }

        /// <summary>
        ///     Delete multiple calculations
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task DeleteItemsAsync(string[] ids)
        {
            if (ids == null) throw new ArgumentNullException(nameof(ids));

            foreach (var id in ids)
            {
                // Migrated from Firestore to Azure Cosmos DB
                await _container.DeleteItemAsync<dynamic>(id, new PartitionKey(id));
            }
        }

        /// <summary>
        ///     Get a calculation
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<dynamic> GetItemAsync(string id)
        {
            try
            {
                // Migrated from Firestore to Azure Cosmos DB
                var response = await _container.ReadItemAsync<dynamic>(id, new PartitionKey(id));
                return response.Resource;
            }
            catch (CosmosException ex) when (ex.StatusCode == HttpStatusCode.NotFound)
            {
                return default;
            }
        }

        /// <summary>
        ///     Get multiple calculations, dependent on the query string
        /// </summary>
        /// <param name="queryString"></param>
        /// <returns></returns>
        public async Task<IEnumerable<dynamic>> GetItemsAsync()
        {
            // Migrated from Firestore to Azure Cosmos DB
            var query = "SELECT * FROM c";
            var queryDefinition = new QueryDefinition(query);
            var queryResultSetIterator = _container.GetItemQueryIterator<dynamic>(queryDefinition);

            var results = new List<dynamic>();
            while (queryResultSetIterator.HasMoreResults)
            {
                var currentResultSet = await queryResultSetIterator.ReadNextAsync();
                results.AddRange(currentResultSet);
            }

            return results;
        }

        /// <summary>
        ///     Update an Calculation
        /// </summary>
        /// <param name="id"></param>
        /// <param name="calculation"></param>
        /// <returns></returns>
        public async Task UpdateItemAsync(string id, JObject calculation)
        {
            if (calculation == null) throw new ArgumentNullException(nameof(calculation));

            var calculationObject = calculation.ToObject<Dictionary<string, object>>();

            try
            {
                if (calculationObject["tighteningMethods"] != null)
                {
                    JArray jArray = (JArray)calculationObject["tighteningMethods"];
                    calculationObject["tighteningMethods"] = jArray.Select(x => x.ToString());
                }
                if (calculationObject["toolList"] != null)
                {
                    JArray jArray = (JArray)calculationObject["toolList"];
                    calculationObject["toolList"] = jArray.Select(x => x.ToString());
                }
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error occured while trying to cast Enumerable from JArray");
                throw;
            }

            // Migrated from Firestore to Azure Cosmos DB
            // Ensure the ID is set in the object
            calculationObject["id"] = id;
            await _container.UpsertItemAsync(calculationObject, new PartitionKey(id));
        }
    }
}