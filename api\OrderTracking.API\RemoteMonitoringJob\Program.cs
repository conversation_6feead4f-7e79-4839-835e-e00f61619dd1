using System;
using System.Threading.Tasks;
using ClientPortal.Shared.Models.MOS;
using ClientPortal.Shared.Services;
using FluentMigrator.Runner;
// Migrated from Google Cloud Diagnostics to Azure Application Insights
// using Google.Cloud.Diagnostics.AspNetCore3;
using Microsoft.ApplicationInsights.WorkerService;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using RemoteMonitoringJob.Migrations;
using RemoteMonitoringJob.Services;

namespace RemoteMonitoringJob
{
    public class Program
    {
        public static async Task Main(string[] args)
        {
            var configBuilder = WebApplication.CreateBuilder(args);
            var app = configBuilder.Build();

            var serviceCollection = new ServiceCollection()
                .AddLogging(b => b.AddConsole())
                .AddSingleton<IConfiguration>(app.Configuration)
                .AddSingleton<IWorkerService, WorkerService>()
                .AddLogging(lb => lb.AddFluentMigratorConsole())
                // Migrated from Google Cloud Diagnostics to Azure Application Insights
                .AddApplicationInsightsTelemetryWorkerService(app.Configuration)
                .AddSingleton<ISensorReadingsSQLService, SensorReadingsSQLService>()
                .AddSingleton<ISmartpimsScraperService, SmartpimsScraperService>()
                .AddFluentMigratorCore()
                        .ConfigureRunner(rb => rb
                            // Add Sql Server support to FluentMigrator
                            .AddSqlServer()
                            // Set the connection string
                            .WithGlobalConnectionString(
                                app.Configuration.GetConnectionString("RemoteMonitoring"))
                            // Define the assembly containing the migrations
                            .ScanIn(typeof(AddSensorReadingsTable).Assembly).For.Migrations())
                .BuildServiceProvider();

            var logger = serviceCollection.GetService<ILoggerFactory>().CreateLogger<Program>();
            logger.LogDebug("Starting WorkerService Execution..");
            var workerService = serviceCollection.GetService<IWorkerService>();
            await workerService.DoAsync();
        }
    }
}