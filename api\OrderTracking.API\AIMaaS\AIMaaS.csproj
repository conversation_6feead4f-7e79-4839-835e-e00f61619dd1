<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Dapper" Version="2.0.123" />
    <!-- Replaced Google Cloud Firestore with Azure Cosmos DB -->
    <PackageReference Include="Microsoft.Azure.Cosmos" Version="3.35.4" />
    <!-- Replaced Google Cloud Storage with Azure Blob Storage -->
    <PackageReference Include="Azure.Storage.Blobs" Version="12.19.1" />
    <!-- Azure Identity for authentication -->
    <PackageReference Include="Azure.Identity" Version="1.10.4" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="4.1.0" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="6.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ClientPortal.Shared\ClientPortal.Shared.csproj" />
  </ItemGroup>

</Project>
