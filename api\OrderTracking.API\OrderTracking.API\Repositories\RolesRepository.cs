using ClientPortal.Shared.Models;
using Google.Cloud.Firestore;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace OrderTracking.API.Repositories
{
    public class RolesRepository : BaseFirestoreRepository<Role, string>, IRolesRepository, IFirestoreRepository
    {
        private string _partitionProperty;
        #region Constructors

        public RolesRepository(IContainerFactory containerFactory, IConfiguration configuration) : base(CreateFirestoreDb(configuration))
        {
            // TODO: Migrate to Azure Cosmos DB - temporarily commented out during migration
            // CollectionReference collectionReference = containerFactory.CreateCollection<RolesRepository>(out var partitionPropertyPath);
            // SetPartitionPropertyFromPath(partitionPropertyPath);

            // For now, use a default partition property path during migration
            SetPartitionPropertyFromPath("/id");
        }

        private static FirestoreDb CreateFirestoreDb(IConfiguration configuration)
        {
            // Implement logic to create and return FirestoreDb instance using the containerFactory
            FirestoreDb firestoreDb = new FirestoreDbBuilder
            {
                ProjectId = configuration["Connections:ProjectId"],
                DatabaseId = configuration["Connections:DatabaseName"]
            }.Build();

            return firestoreDb;
        }

        private void SetPartitionPropertyFromPath(string path)
        {
            if (path == null) throw new ArgumentNullException(nameof(path));

            // Split the path and set partition property based on some logic
            // Example:
            var pathSegments = path.Split('/');
            _partitionProperty = pathSegments.LastOrDefault();
        }

        public RolesRepository(FirestoreDb container)
            : base(container)
        {
        }

        #endregion

        #region Interface Implementation

        public override async Task<Role> GetAsync(string entityId) => await GetAsync(entityId, entityId);

        public override async Task<Role> AddAsync(Role entity)
        {
            try
            {
                DocumentReference docRef = FirestoreDb.Collection(base.Collection.Id).Document(entity.Id.ToString());
                WriteResult writeResult = await docRef.SetAsync(entity);
                Console.WriteLine(writeResult.UpdateTime);

                return await GetAsync(entity.Id);
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public async Task<IEnumerable<Role>> GetRolesForGroupAsync(string group)
        {
            var collection = FirestoreDb.Collection("roles");

            // Construct query to filter roles based on the specified group
            var query = collection.WhereEqualTo("group", group);

            // Execute the query and retrieve the documents
            var querySnapshot = await query.GetSnapshotAsync();

            // Convert the documents to Role objects
            var roles = querySnapshot.Documents.Select(doc => doc.ConvertTo<Role>());

            return roles;
        }

        public override async Task RemoveAsync(string id) => await base.RemoveAsync(id);

        //upadate with ID change
        public async Task<Role> UpsertAsync(Role entity, string originalId)
        {
            var newRole = await AddAsync(entity); //adds new role
            await base.RemoveAsync(originalId);   //removes original role
            return newRole;
        }

        public override async Task<Role> UpdateAsync(Role entity, string originalId) =>
            await UpdateAsync(entity, originalId);

        public override Task RemoveAsync(string id, string partitionId)
        {
            throw new NotImplementedException();
        }

        #endregion
    }
}