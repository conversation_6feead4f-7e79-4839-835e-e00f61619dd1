import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
// Migrated from Firebase to Azure AD B2C
// import { Auth, authState, signInWithCustomToken } from '@angular/fire/auth';
// import { Firestore, doc, docData } from '@angular/fire/firestore';
import { MsalService } from '@azure/msal-angular';
import { LoadOptions } from 'devextreme/data';
import CustomStore from 'devextreme/data/custom_store';
import DataSource from 'devextreme/data/data_source';
import { ToastrService } from 'ngx-toastr';
import {
    BehaviorSubject,
    EMPTY,
    Observable,
    ReplaySubject,
    combineLatest,
    from,
    lastValueFrom,
    of
} from 'rxjs';
import {
    catchError,
    delay,
    distinctUntilChanged,
    filter,
    finalize,
    map,
    shareReplay,
    switchMap,
    take,
    tap
} from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { debug, tapWhen, userHasSomesRoles } from '../../core/operators';
import { UserProfile } from '../../profile/models';
import {
    AssetWalkdown,
    FullPackage
} from '../../report/models/report-source.model';
import { httpParamsFromLoadOptions } from '../../shared/helpers/remote-operations';
import { UsersService } from '../../shared/services';
import {
    ActivityUpdate,
    AddRemoveProjectAsset,
    Asset,
    AssetAccessUpdate,
    AssetDetailsPhotoTransport,
    AssetPPEViewModel,
    AssetVm,
    Contact,
    FirebaseUser,
    FiveSeventyAssetDetails,
    FiveTenAssetDetailsUpdate,
    InspectionInfo,
    InspectionResult,
    LeakReport,
    LeakReportGridRow,
    LeakReportInfo,
    LeakReportWorkDetails,
    Location,
    LocationTransport,
    NewAsset,
    NewProject,
    NewTask,
    NewWorkOrder,
    PhotoDelete,
    PhotoDescriptionUpdate,
    PhotoGroupTransport,
    ProjectAndTaskActivities,
    ProjectUpdate,
    ProjectVm,
    PublishUnpublishWorkOrderUpdate,
    SixFiftyThreeAssetDetails,
    TaskUpdate,
    TaskVM,
    VisualInspectionPhotoTransport,
    WorkOrder,
    WorkOrderDetailTabInfo
} from '../models';
import { FieldWorkCompletedTransport } from '../models/transport-objects/field-work-completed-transport';

export type ReportTransport = {
    workOrderId: string;
    projectId: string;
    walkDownTaskId?: string;
    internalTaskId?: string;
    externalTaskId?: string;
    reportType: string;
    taskType: string;
    assetCategory: string;
};

@Injectable()
export class ApmService {
    private readonly _locations = new ReplaySubject<Location[]>();
    private readonly _projectAssets = new ReplaySubject<Asset[]>();
    private readonly _locationAssets = new ReplaySubject<Asset[]>();

    private readonly _gettingProjects = new ReplaySubject<boolean>();
    private readonly _gettingAssets = new ReplaySubject<boolean>();
    private readonly _gettingStatusesByMonth = new ReplaySubject<boolean>();
    private readonly _gettingStatusesByWeek = new ReplaySubject<boolean>();
    private readonly _gettingAssetCategoriesSummary =
        new ReplaySubject<boolean>();
    private readonly _gettingAssetsByAreaAndType = new ReplaySubject<boolean>();
    private readonly _gettingInspectionStatuses = new ReplaySubject<boolean>();
    private readonly _gettingSummaryInfo = new ReplaySubject<boolean>();
    private readonly _gettingActivitySummary = new ReplaySubject<boolean>();
    private readonly _gettingInspectionsWithoutDueDate =
        new ReplaySubject<boolean>();
    private readonly _gettingTasks = new ReplaySubject<boolean>();
    private readonly _gettingWorkOrder = new ReplaySubject<boolean>();
    private readonly _updatingTask = new ReplaySubject<boolean>();
    private readonly _creatingTask = new ReplaySubject<boolean>();
    private readonly _BuSelected = new ReplaySubject<boolean>();
    private _selectedBU = new BehaviorSubject<string>(null);

    // Migrated from Firebase to Azure AD B2C
    readonly user$ = this._msalService.instance.getAllAccounts().length > 0
        ? of(this._msalService.instance.getAllAccounts()[0])
        : of(null);

    // Migrated from Firebase to Azure AD B2C - get user from backend API
    readonly apmUser$ = this.user$.pipe(
        filter(Boolean),
        switchMap((user) =>
            this._http.get(`${environment.api.url}/users/${user.username || user.localAccountId}`)
        ),
        map((data) => data as any), // Replaced FirebaseUser with any
        debug('APM User'),
        shareReplay()
    );

    photoSas$ = this.getPhotoSasToken().pipe(
        debug('photoSas Token:'),
        shareReplay()
    );

    locations$ = combineLatest([
        this._locations.asObservable(),
        this._selectedBU.asObservable()
    ]).pipe(
        map(([locations, selectedBU]) => {
            const filteredLocations = locations.filter(
                (l) => l.businessUnitId?.currentValue == selectedBU
            );
            return filteredLocations;
        })
    );

    projectAssets$ = this._projectAssets.asObservable();
    locationAssets$ = this._locationAssets.asObservable();
    gettingProjects$ = this._gettingProjects.asObservable();
    gettingStatusesByMonth$ = this._gettingStatusesByMonth.asObservable();
    gettingStatusesByWeek$ = this._gettingStatusesByWeek.asObservable();
    gettingAssetCategoriesSummary$ =
        this._gettingAssetCategoriesSummary.asObservable();
    gettingAssetsByAreaAndType$ =
        this._gettingAssetsByAreaAndType.asObservable();
    gettingInspectionStatuses$ = this._gettingInspectionStatuses.asObservable();
    gettingSummaryInfo$ = this._gettingSummaryInfo.asObservable();
    gettingActivitySummary$ = this._gettingActivitySummary.asObservable();
    gettingAssets$ = this._gettingAssets.asObservable();
    gettingInspectionsWithoutDueDate$ =
        this._gettingInspectionsWithoutDueDate.asObservable();
    gettingTasks$ = this._gettingTasks.asObservable();
    gettingWorkOrder$ = this._gettingWorkOrder.asObservable();
    updatingTask$ = this._updatingTask.asObservable();
    creatingTask$ = this._creatingTask.asObservable();
    buSelected$ = this._BuSelected.asObservable();
    allowEditing$ = this._users.currentProfile$.pipe(
        userHasSomesRoles(['apm:edit', 'app:admin', 'apm:admin'])
    );
    selectedBU$ = this._selectedBU.asObservable().pipe(
        filter(Boolean),
        distinctUntilChanged(),
        tap(() => {
            this.projectDataSource.reload();
            this.assetsDataSource.reload();
        }),
        shareReplay(1)
    );

    projectDataSource: DataSource<ProjectVm, string> = new DataSource({
        store: new CustomStore({
            key: 'id',
            byKey: (key: any) =>
                lastValueFrom(
                    this._http.get(`${environment.api.url}/APM/Project/${key}`)
                ),
            load: (loadOptions) =>
                lastValueFrom(this.loadProjectNames(loadOptions)),
            useDefaultSearch: true
        }),
        paginate: false
    });

    assetsDataSource: DataSource<AssetVm, string> = new DataSource({
        store: new CustomStore({
            key: 'id',
            byKey: (key: string) => lastValueFrom(this.getAsset(key)),
            load: (options) => lastValueFrom(this.loadAllAssets(options)),
            update: (key: string, values: any) =>
                lastValueFrom(this.updateAsset(values)),
            useDefaultSearch: true
        })
    });

    constructor(
        private readonly _http: HttpClient,
        private readonly _toasts: ToastrService,
        private readonly _users: UsersService,
        // Migrated from Firebase to Azure AD B2C
        private readonly _msalService: MsalService
    ) {}

    get selectedBU() {
        return this._selectedBU.value;
    }

    selectBusinessUnit(selectedItem: string) {
        this._users.currentProfile$
            .pipe(
                take(1),
                switchMap((user: UserProfile) => {
                    if (user.selectedBusinessUnit === selectedItem)
                        return of(undefined);
                    user.selectedBusinessUnit = selectedItem;
                    return this._users.update(user, user.id);
                }),
                tap(() => {
                    this._selectedBU.next(selectedItem);
                    this._BuSelected.next(true);
                })
            )
            .subscribe();
    }

    getProject(projectId: string): Observable<ProjectVm> {
        return this._http
            .get<ProjectVm>(`${environment.api.url}/APM/Project/${projectId}`)
            .pipe(debug('Project'));
    }

    getLocation(locationId: string): Observable<Location> {
        return this._http
            .get<any>(`${environment.api.url}/APM/Location/${locationId}`)
            .pipe(debug('location'));
    }

    loadLocations(options: LoadOptions) {
        const params = httpParamsFromLoadOptions(options);
        return this._http.get<Location[]>(
            `${environment.api.url}/APM/Locations`,
            { params }
        );
    }

    getLocations() {
        const locations$ = this._http
            .get<{ data: Location[] }>(`${environment.api.url}/APM/Locations`)
            .pipe(
                map((response) => response.data),
                debug('Locations'),
                shareReplay()
            );

        locations$.subscribe((locations) => this._locations.next(locations));
        return locations$;
    }

    loadProjects(options: LoadOptions): Observable<any> {
        const params = httpParamsFromLoadOptions(options);
        return this._http.get<ProjectVm>(
            `${environment.api.url}/APM/Projects`,
            {
                params
            }
        );
    }

    loadProjectNames(options: LoadOptions): Observable<any> {
        const params = httpParamsFromLoadOptions(options);
        return this._http.get<ProjectVm>(
            `${environment.api.url}/APM/Projects/names`,
            {
                params
            }
        );
    }

    postProject(newProject: NewProject) {
        const project$ = this._http
            .post<ProjectVm>(`${environment.api.url}/APM/Project`, newProject)
            .pipe(
                debug('Post:Project'),
                switchMap((project) =>
                    this._http.get<ProjectVm[]>(
                        `${environment.api.url}/APM/Projects`
                    )
                ),
                shareReplay()
            );
        return project$;
    }

    putProject(projectUpdate: Partial<ProjectUpdate>) {
        const project$ = this._http
            .put<ProjectVm>(`${environment.api.url}/APM/Project`, projectUpdate)
            .pipe(shareReplay(), debug('Put:Project'));

        return project$;
    }

    putWorkOrderTask(inspectionResult: InspectionResult) {
        return this._http.put<any>(
            `${environment.api.url}/APM/WorkOrder/VisualInspection`,
            inspectionResult
        );
    }

    createLocation(location: LocationTransport) {
        const command$ = this._http
            .post<Location>(`${environment.api.url}/APM/Location`, location)
            .pipe(debug('POST:Location'), delay(300), shareReplay());
        const locations$ = command$.pipe(
            switchMap(() =>
                this._http.get<Location[] | { data: Location[] }>(
                    `${environment.api.url}/APM/Locations`
                )
            ),
            shareReplay()
        );
        locations$.subscribe((locations) => {
            if ('data' in locations) this._locations.next(locations.data);
            else this._locations.next(locations);
        });
        return command$;
    }

    putLocation(location: LocationTransport) {
        const locationInserted$ = this._http
            .put<Location>(`${environment.api.url}/APM/Location`, location)
            .pipe(debug('Put:Location'), delay(300), shareReplay());
        const location$ = locationInserted$.pipe(
            switchMap((project) =>
                this._http.get<Location[] | { data: Location[] }>(
                    `${environment.api.url}/APM/Locations`
                )
            ),
            shareReplay()
        );
        location$.subscribe((locations) => {
            if ('data' in locations) this._locations.next(locations.data);
            else this._locations.next(locations);
        });
        return locationInserted$;
    }

    postAsset(newAsset: NewAsset) {
        const assetCreation = this._http
            .post<Asset>(`${environment.api.url}/APM/Asset`, newAsset)
            .pipe(debug('Post:Asset'), shareReplay());
        assetCreation
            .pipe(
                delay(500),
                switchMap((asset) => this.getProjectAssets(newAsset.projectId)),
                switchMap((asset) => this.getProject(newAsset.projectId)),
                switchMap((project) =>
                    this.getLocationAssets(project.locationId)
                ),
                shareReplay()
            )
            .subscribe();
        return assetCreation;
    }

    addRemoveProjectAsset(
        addRemoveAsset: AddRemoveProjectAsset,
        locationId: string
    ) {
        return this._http
            .put<ProjectVm>(
                `${environment.api.url}/APM/AddRemoveProjectAsset`,
                addRemoveAsset
            )
            .pipe(debug('Post:AddRemoveProjectAsset'))
            .pipe(
                switchMap((project) =>
                    this.getProjectAssets(addRemoveAsset.projectId)
                ),
                switchMap((project) => this.getLocationAssets(locationId)),
                shareReplay()
            );
    }

    getAsset(id: string) {
        return this._http.get<AssetVm>(
            `${environment.api.url}/APM/Assets/${id}`
        );
    }

    loadAllAssets(options: LoadOptions) {
        const params = httpParamsFromLoadOptions(options);
        return this._http.get(`${environment.api.url}/APM/LoadAssets`, {
            params
        });
    }

    loadAssets(options: LoadOptions, projectId: string) {
        const params = httpParamsFromLoadOptions(options);

        return this._http.get(
            `${environment.api.url}/APM/LoadProjectAssets/${projectId}`,
            {
                params
            }
        );
    }

    getProjectAssets(projectId: string): Observable<Asset[]> {
        this._gettingAssets.next(true);
        const projectAssets$ = this._http
            .get<Asset[]>(
                `${environment.api.url}/APM/ProjectAssets/${projectId}`,
                {
                    params: new HttpParams()
                }
            )
            .pipe(debug('ProjectAssets'), shareReplay());
        projectAssets$
            .pipe(finalize(() => this._gettingAssets.next(false)))
            .subscribe((assets) => this._projectAssets.next(assets));
        return projectAssets$;
    }

    getLocationAssets(locationId: string): Observable<Asset[]> {
        this._gettingAssets.next(true);
        const locationAssets$ = this._http
            .get<Asset[]>(
                `${environment.api.url}/APM/LocationAssets/${locationId}`,
                {
                    params: new HttpParams()
                }
            )
            .pipe(debug('LocationAssets'), shareReplay());
        locationAssets$
            .pipe(finalize(() => this._gettingAssets.next(false)))
            .subscribe((assets) => this._locationAssets.next(assets));
        return locationAssets$;
    }

    loadWorkOrders(options: LoadOptions, assetId?: string) {
        const params = httpParamsFromLoadOptions(options);

        let request = assetId
            ? `${environment.api.url}/APM/Assets/${assetId}/WorkOrders`
            : `${environment.api.url}/APM/WorkOrdersVM`;

        return this._http.get<any>(request, { params }).pipe(debug('TESTING'));
    }

    loadWorkOrder(id: string) {
        return this._http.get(`${environment.api.url}/LoadWorkOrder/${id}`);
    }

    getWorkOrder(
        workOrderId: string,
        projectId?: string
    ): Observable<WorkOrder> {
        this._gettingWorkOrder.next(true);

        let request = projectId
            ? `${environment.api.url}/APM/Projects/${projectId}/WorkOrders/${workOrderId}`
            : `${environment.api.url}/APM/WorkOrder/${workOrderId}`;

        return this._http.get<WorkOrder>(request).pipe(
            finalize(() => this._gettingWorkOrder.next(false)),
            debug('Work Order By Id'),
            catchError(() => of(undefined))
        );
    }

    updateWorkOrder(update: Partial<WorkOrderDetailTabInfo | InspectionInfo>) {
        return this._http
            .put<WorkOrder>(`${environment.api.url}/APM/WorkOrder`, update)
            .pipe(debug('UPDATE: WorkOrder'));
    }

    updateFieldWorkCompletedDate(update: FieldWorkCompletedTransport) {
        return this._http
            .put<WorkOrder>(
                `${environment.api.url}/APM/FieldWorkCompleted`,
                update
            )
            .pipe(debug('UPDATE: FieldWorkCompletedDate'));
    }

    updateFiveTenAssetDetails(update: FiveTenAssetDetailsUpdate) {
        return this._http
            .put(`${environment.api.url}/APM/FiveTenAssetDetails`, update)
            .pipe(debug('UPDATE: FiveTenAssetDetails'));
    }

    getUsers() {
        return this._http
            .get<any[]>(`${environment.api.url}/APM/Users`)
            .pipe(debug('Users'));
    }

    createTask(newTask: NewTask) {
        this._creatingTask.next(true);
        return this._http
            .post<any>(`${environment.api.url}/APM/Task`, newTask)
            .pipe(
                debug('POST: Task'),
                finalize(() => this._creatingTask.next(false)),
                catchError((error) => {
                    let message = 'Unknown error occurred';

                    if (error.error?.errors) {
                        message = `
<p>The following errors occurred:</p>
<ul>
${Object.values(error.error.errors)
    .map((err) => `<li>${err}</li>`)
    .join('')}
</ul>`;
                    }

                    this._toasts.error(message, 'An error occurred', {
                        enableHtml: true,
                        disableTimeOut: true
                    });
                    return EMPTY;
                })
            );
    }

    updateWorkOrderHeader(taskUpdate: TaskUpdate) {
        return this._http
            .put<any>(
                `${environment.api.url}/APM/TaskHeader/${taskUpdate.databaseId}`,
                taskUpdate
            )
            .pipe(debug('PUT: WorkOrderHeader:Task'));
    }

    updateTask(taskUpdate: TaskUpdate) {
        this._updatingTask.next(true);
        return this._http
            .put<any>(
                `${environment.api.url}/APM/Task/${taskUpdate.databaseId}`,
                taskUpdate
            )
            .pipe(
                finalize(() => this._updatingTask.next(false)),
                debug('PUT: Task')
            );
    }

    publishUnpublishWorkOrder(
        publishUnpublishUpdate: PublishUnpublishWorkOrderUpdate
    ) {
        return this._http
            .put<WorkOrder>(
                `${environment.api.url}/APM/PublishUnpublishWorkOrder`,
                publishUnpublishUpdate
            )
            .pipe(debug('PUT: Publish/Unpublish WorkOrder'));
    }

    createWorkOrder(newWorkOrder: NewWorkOrder) {
        return this._http
            .post<WorkOrder>(
                `${environment.api.url}/APM/WorkOrders`,
                newWorkOrder
            )
            .pipe(debug('POST: Work Order'));
    }

    updateAsset(e: AssetVm) {
        return this._http.put<AssetVm>(`${environment.api.url}/APM/Asset`, e);
    }

    updateAssetPPE(e: Partial<AssetPPEViewModel>) {
        return this._http
            .put<Asset>(`${environment.api.url}/APM/AssetPPE/${e.id}`, e)
            .pipe(debug('PUT: Asset PPE'));
    }

    getWorkOrderFiles(id: string) {
        return this._http
            .get<any[]>(`${environment.api.url}/APM/InspectionFiles/${id}`)
            .pipe(debug('GET: WO Files'));
    }

    downloadInspectionFile(workOrderId: string, fileName: string) {
        return this._http
            .get(
                `${environment.api.url}/APM/InspectionFiles/${workOrderId}/${fileName}`,
                { responseType: 'blob' }
            )
            .pipe(debug('GET: Inspection File (Download)'));
    }

    deleteInspectionFile(workOrderId: any, fileName: any) {
        return this._http
            .delete(
                `${environment.api.url}/APM/InspectionFiles/${workOrderId}/${fileName}`
            )
            .pipe(debug('DELETE: Inspection File'));
    }

    updateAssetAccess(update: AssetAccessUpdate) {
        return this._http
            .put<Asset>(`${environment.api.url}/APM/AssetAccess`, update)
            .pipe(debug('UPDATE: AssetAccess'));
    }

    getAllActivity(projectId: string): Observable<ProjectAndTaskActivities> {
        return this._http
            .get<ProjectAndTaskActivities>(
                `${environment.api.url}/APM/AllActivity/${projectId}`
            )
            .pipe(debug('GET: All Activity'));
    }

    updateProjectActivity(update: ActivityUpdate) {
        const projectActivityUpdate$ = this._http
            .put<ProjectVm>(
                `${environment.api.url}/APM/ProjectActivity`,
                update
            )
            .pipe(
                debug('UPDATE: ProjectActivity'),
                switchMap((project) =>
                    this._http.get<ProjectVm[]>(
                        `${environment.api.url}/APM/Projects`
                    )
                ),
                shareReplay()
            );

        return projectActivityUpdate$;
    }

    deleteVisualInspectionPhoto(e: VisualInspectionPhotoTransport) {
        return this._http
            .delete(`${environment.api.url}/APM/VisualInspectionPhoto`, {
                body: e
            })
            .pipe(debug('DELETE: Visual Inspection Photo'));
    }

    updateVisualInspectionPhotoDescription(e: VisualInspectionPhotoTransport) {
        return this._http
            .put(
                `${environment.api.url}/APM/VisualInspectionPhotoDescription`,
                e
            )
            .pipe(debug('UPDATE: Visual Inspection Photo description'));
    }

    deleteAssetDetailsPhoto(e: AssetDetailsPhotoTransport) {
        return this._http
            .delete(`${environment.api.url}/APM/AssetDetailsPhotos/`, {
                body: e
            })
            .pipe(debug('DELETE: AssetDetails Photo'));
    }

    updateAssetDetailsPhotoDescription(e: AssetDetailsPhotoTransport) {
        return this._http
            .put(`${environment.api.url}/APM/AssetDetailsPhotos`, e)
            .pipe(debug('UPDATE: Asset Details Photo description'));
    }

    updateSixFiftyThreeAssetDetails(e: Partial<SixFiftyThreeAssetDetails>) {
        return this._http
            .put(`${environment.api.url}/APM/SixFiftyThreeWalkDown`, e)
            .pipe(debug('UPDATE: 653 WalkDown'));
    }

    updateFiveSeventyAssetDetails(e: Partial<FiveSeventyAssetDetails>) {
        return this._http
            .put(`${environment.api.url}/APM/FiveSeventyWalkDown`, e)
            .pipe(debug('UPDATE: 570 WalkDown'));
    }

    getAssetWalkdownData(payload: ReportTransport) {
        return this._http
            .post<AssetWalkdown>(
                `${environment.api.url}/APM/JasperSoft`,
                payload
            )
            .pipe(debug('POST: JasperSoft report'));
    }

    downloadFileFromUrl(url: string) {
        return this._http.get(url, { responseType: 'blob' });
    }

    updateClientContact(e: Contact, projectid: string) {
        return this._http.put(
            `${environment.api.url}/APM/Client/${projectid}`,
            e
        );
    }

    createClientContact(e: Contact, projectid: string) {
        return this._http.post(
            `${environment.api.url}/APM/Client/${projectid}`,
            e
        );
    }

    deleteClientContact(contactId: string, projectid: string) {
        return this._http.delete(
            `${environment.api.url}/APM/Client/${projectid}/${contactId}`
        );
    }

    getTask(id: string) {
        return this._http.get(`${environment.api.url}/APM/Task/${id}`);
    }

    loadTasks(options: LoadOptions) {
        this._gettingTasks.next(true);
        const params = httpParamsFromLoadOptions(options);
        return this._http
            .get<{ data: TaskVM[] }>(`${environment.api.url}/APM/Tasks`, {
                params
            })
            .pipe(finalize(() => this._gettingTasks.next(false)));
    }

    getPhotoSasToken() {
        var photosas = this._http.get<any>(
            `${environment.api.url}/APM/BlobSas`,
            { responseType: 'text' as 'json' }
        );
        return photosas;
    }

    getLeakReports(): Observable<LeakReport[]> {
        return this._http
            .get<LeakReport[]>(`${environment.api.url}/LeakReport`)
            .pipe(debug('Leak Reports'));
    }

    createLeakReport(report: LeakReportGridRow) {
        return this._http
            .post(`${environment.api.url}/LeakReport`, report)
            .pipe(debug('CREATE Leak Report'));
    }

    updateLeakReportStatus(reportID: string, status: string) {
        return this._http
            .put(`${environment.api.url}/LeakReport/${reportID}`, {
                status
            })
            .pipe(debug('UPDATE Leak Report STATUS'));
    }

    updateLeakReportInfo(id: string, info: Partial<LeakReportInfo>) {
        return this._http
            .put(`${environment.api.url}/LeakReport/LeakReportInfo/${id}`, info)
            .pipe(debug('UPDATE Leak Report Info'));
    }

    updateLeakReportPhotoGroup(
        reportId: string,
        photoGroupDatabaseId: string,
        update: Partial<PhotoGroupTransport>
    ) {
        return this._http
            .put(
                `${environment.api.url}/LeakReport/LeakReportPhotoGroup/${photoGroupDatabaseId}`,
                { leakReportId: reportId, ...update }
            )
            .pipe(debug('PUT Photo Group'));
    }

    deleteLeakReportGroupPhoto(
        photoId: string,
        ids: { reportId: string; photoGroupId: string }
    ) {
        return this._http
            .delete(
                `${environment.api.url}/LeakReport/LeakReportPhoto/${photoId}`,
                {
                    body: ids
                }
            )
            .pipe(debug('DELETE Leak Report Photo'));
    }

    updateLeakReportWorkDetails(
        id: string,
        info: Partial<LeakReportWorkDetails>
    ) {
        return this._http
            .put(
                `${environment.api.url}/LeakReport/LeakReportWorkDetails/${id}`,
                info
            )
            .pipe(debug('UPDATE Leak Report Work Details'));
    }

    updatePhotoDescription(update: PhotoDescriptionUpdate) {
        return this._http
            .put(
                `${environment.api.url}/LeakReport/LeakReportingPhotoDescription`,
                update
            )
            .pipe(debug('UPDATE Leak Reporting Photo Description'));
    }

    deletePhoto(photoDelete: PhotoDelete) {
        return this._http
            .delete(`${environment.api.url}/LeakReport/LeakReportingPhoto`, {
                body: photoDelete
            })
            .pipe(debug('DELETE Leak Reporting Photo'));
    }

    generateReport(reportId: string) {
        return this._http
            .post(
                `${environment.api.url}/LeakReport/LeakReportingReport/${reportId}`,
                null
            )
            .pipe(debug('POST: Leak Reporting report'));
    }

    getFullPackage(reportTransport: ReportTransport) {
        return this._http
            .post<FullPackage>(
                `${environment.api.url}/APM/ReportObject`,
                reportTransport
            )
            .pipe(debug('GET: Full Package report'));
    }

    getMyBusinessUnits() {
        return this._http
            .get(`${environment.api.url}/APM/MyBusinessUnits`)
            .pipe(debug('GET: My Business Units'));
    }

    getSignedUrl(fileName: string) {
        return this._http.get(
            `${environment.api.url}/APM/BlobSasObject/${fileName}`,
            {
                responseType: 'text'
            }
        );
    }

    private projectIdsParams(projectIds: string[]): HttpParams {
        let params = new HttpParams();
        projectIds?.forEach((id) => (params = params.append('projectIds', id)));
        return params;
    }

    // Migrated from Firebase to Azure AD B2C - no longer needed
    private signInWithCustomToken() {
        // Azure AD B2C handles authentication automatically through MSAL
        return of(null);
    }
}
