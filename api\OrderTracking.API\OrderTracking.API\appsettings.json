{
  "ConnectionStrings": {
    "RemoteMonitoring": "***REMOVED***"
  },
  "AnteaDb": {
    "ConnectionString": "***REMOVED***"
  },
  "AzureAdB2C": {
    "Instance": "https://teamincb2c.b2clogin.com/",
    "ClientId": "***REMOVED***",
    "Domain": "teamincb2c.onmicrosoft.com",
    "SignUpSignInPolicyId": "B2C_1_signupsignin"
  },
  "AzureAd": {
    "AuthenticationMode": "ServicePrincipal",
    "AuthorityUri": "https://login.microsoftonline.com/organizations/",
    "ClientId": "0a66b99f-3478-4b56-91c4-d674b0225f9d",
    "TenantId": "3cfc49f9-956e-4e4e-a1b6-e03368c2e448",
    "Scope": [ "https://analysis.windows.net/powerbi/api/.default" ],
    "PbiUsername": "",
    "PbiPassword": "",
    "ClientSecret": "***REMOVED***"
  },
  "BlobStorage": {
    "BlobEndpoint": "https://tdclientportalfiles.blob.core.windows.net/",
    "AccountName": "tdclientportalfiles",
    //"ConnectionString": "***REMOVED***",
    //"Key": "***REMOVED***",
    "APMContainer": "apmdev",
    "APMKey": "***REMOVED***",
    //"APMReportingKey": "***REMOVED***",
    "APMReportingContainer": "apmdev-reporting",
    "APMStorageAccountName": "stakrakendev001",
    "APMBlobContainerName": "apm-dev",
    "APMWOStorageAccountName": "stakrakendev001",
    "APMWOBlobContainerName": "apm-workorders-dev",
    "KeyVaultName": "kv-kraken-dev-001"
  },
  //"CredoSoftData": {
  //  "ConnectionString": "***REMOVED***",
  //  "FileShareSAS": "***REMOVED***"
  //},
  "Connections": {
    "Database": "ClientPortalDevTestStaging",
    "UserProfiles": "user-profiles",
    "Roles": "roles",
    "EquipmentRequests": "equipment-requests",
    "FlangeCalculations": "flange-calculations",
    "Notifications": "notifications",
    "ReleaseNotes": "release-notes",
    "AuthHistory": "auth-history",
    "Endpoint": "https://opt-dev-cosmosdb-001.documents.azure.com:443/",
    "AuthKey": "",
    "ConnectionString": "AccountEndpoint=https://opt-dev-cosmosdb-001.documents.azure.com:443/;AccountKey=****************************************************************************************;",
    "KeyVaultName": "kv-kraken-dev-001",
    "ResourceGroupName": "rg-kraken-dev-001",
    "SubscriptionId": "***AZURE_SUBSCRIPTION_ID***",
    "DatabaseName": "cosmos-clientportalapi-dev-001",
    "AnteaAttachmentsBlobContainer": "antea-attachments-dev",
    "AnteaSubmissionsBlobContainer": "antea-submissions-dev"
  },
  "APM": {
    "Environment": "testing",
    "Firebase": "***REMOVED***",
    "CmsSettings": {
      "CMSRootObjectName": "Inspection",
      "CMSBaseInspectionsFirestoreCollectionName": "cms-base-inspections",
      "CMSClientInspectionsFirestoreCollectionName": "cms-client-inspections",
      "CMSClientInspectionsSitesFirestoreSubcollectionName": "sites",
      "CMSClientInspectionsSitesInspectionsFirestoreSubcollectionName": "inspections",
      "CMSClientInspectionsAllSitesFirestoreSubcollectionName": "all-sites-inspections",
      "CMSDistrictInspectionsFirestoreCollectionName": "cms-district-inspections",
      "CMSDistrictInspectionsSitesFirestoreSubcollectionName": "sites",
      "CMSDistrictInspectionsSitesInspectionsFirestoreSubcollectionName": "inspections",
      "CMSDistrictInspectionsAllSitesFirestoreSubcollectionName": "all-sites-inspections"
    }
  },
  "PowerBI": {
    "WorkspaceId": "f41e97a3-6f9f-43a5-993c-0caeeacbdc73",
    "ReportId": "05a80050-2764-46ce-8156-f6fb06767c72"
  },
  //"Azure:SignalR:ConnectionString": "***REMOVED***",
  "ZDapperPlus": {
    "LicenseName": "4647;701-teaminc.com",
    "LicenseKey": "***REMOVED***"
  },
  //"ApplicationInsights": {
  //  "InstrumentationKey": "***REMOVED***"
  //},
  "SendGrid": {
    "APIKey": "***REMOVED***"
  },
  "Swagger": {
    "ClientId": "***REMOVED***",
    "ClientSecret": "***REMOVED***"
  },
  "FSMConfig": {
    "UserName": "***REMOVED***",
    "Password": "***REMOVED***",
    "Url": "***REMOVED***",
    "UpdateFlowUrl": "***REMOVED***"
  },
  "Logging": {
    "ApplicationInsights": {
      "LogLevel": {
        "Default": "Information",
        "Microsoft": "Error"
      }
    },
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Debug"
    },
    "Console": {
      "IncludeScopes": true
    }
  },
  "DeploymentEnvironment": "Local",
  "AllowedHosts": "*",
  "Clients": {
    "ClientPortal": "http://localhost:4200"
  }
}