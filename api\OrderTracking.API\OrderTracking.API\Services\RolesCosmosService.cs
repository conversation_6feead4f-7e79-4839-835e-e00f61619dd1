using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ClientPortal.Shared.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using OrderTracking.API.Repositories;

namespace OrderTracking.API.Services
{
    /// <summary>
    /// Azure Cosmos DB service for Role operations (migrated from Firebase)
    /// </summary>
    public class RolesCosmosService : RolesCosmosRepository, IRolesService
    {
        #region Fields and Constants

        private readonly IUserProfileRepository _userProfileRepository;
        private readonly IAuthHistoryService _authHistory;
        private readonly IHttpContextAccessor _httpContextAccessor;

        #endregion

        #region Constructors

        public RolesCosmosService(IContainerFactory containerFactory, IServiceProvider serviceProvider, IConfiguration configuration) 
            : base(containerFactory, configuration)
        {
            _authHistory = (IAuthHistoryService)serviceProvider.GetService(typeof(IAuthHistoryService));
            _httpContextAccessor = (IHttpContextAccessor)serviceProvider.GetService(typeof(IHttpContextAccessor));

            _userProfileRepository = new UserProfileCosmosRepository(containerFactory, configuration);
        }

        #endregion

        #region Interface Implementation

        public new async Task<Role> AddAsync(Role role)
        {
            try
            {
                role = VerifyProperties(role);
                var newRole = await base.AddAsync(role);
                if (newRole != null) await CreateChangeEventAsync(null, newRole);

                return newRole;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public async Task<IEnumerable<string>> GetGroupsAsync()
        {
            try
            {
                var roles = await GetAllAsync();
                return roles.Select(r => r.Group).Distinct();
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public new async Task<Role> UpdateAsync(Role role)
        {
            try
            {
                var originalRole = await GetAsync(role.Id);
                role = VerifyProperties(role);
                var updatedRole = await base.UpdateAsync(role);
                if (updatedRole != null) await CreateChangeEventAsync(originalRole, updatedRole);

                return updatedRole;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public new async Task RemoveAsync(string id)
        {
            try
            {
                var originalRole = await GetAsync(id);
                await base.RemoveAsync(id);
                if (originalRole != null) await CreateChangeEventAsync(originalRole, null);
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        #endregion

        #region Private Methods

        private Role VerifyProperties(Role role)
        {
            if (role == null) return null;

            // Ensure required properties are set
            if (string.IsNullOrEmpty(role.Id))
            {
                role.Id = Guid.NewGuid().ToString();
            }

            if (role.CreatedDate == default)
            {
                role.CreatedDate = DateTime.UtcNow;
            }

            role.ModifiedDate = DateTime.UtcNow;

            return role;
        }

        private async Task CreateChangeEventAsync(Role originalRole, Role newRole)
        {
            try
            {
                if (_authHistory == null || _httpContextAccessor?.HttpContext == null) return;

                var userEmail = _httpContextAccessor.HttpContext.User?.Identity?.Name;
                if (string.IsNullOrEmpty(userEmail)) return;

                var changeEvent = new AuthHistory
                {
                    Id = Guid.NewGuid().ToString(),
                    UserEmail = userEmail,
                    TargetUserEmail = userEmail, // For role changes, target is the same as user
                    Action = newRole == null ? "DELETE_ROLE" : (originalRole == null ? "CREATE_ROLE" : "UPDATE_ROLE"),
                    Timestamp = DateTime.UtcNow,
                    Details = $"Role '{newRole?.Name ?? originalRole?.Name}' {(newRole == null ? "deleted" : (originalRole == null ? "created" : "updated"))}"
                };

                await _authHistory.AddAsync(changeEvent);
            }
            catch (Exception e)
            {
                // Log but don't throw - change events are not critical
                Console.WriteLine($"Failed to create change event: {e.Message}");
            }
        }

        #endregion
    }
}
